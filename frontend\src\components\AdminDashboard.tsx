import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Branch } from '../types';
import { branchAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import BranchForm from './BranchForm';
import ConfirmDialog from './ConfirmDialog';

const AdminDashboard: React.FC = () => {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingBranch, setEditingBranch] = useState<Branch | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [branchToDelete, setBranchToDelete] = useState<Branch | null>(null);
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    fetchBranches();
  }, []);

  const fetchBranches = async () => {
    try {
      setLoading(true);
      const data = await branchAPI.getAll();
      setBranches(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch branches');
      console.error('Error fetching branches:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = (branch: Branch) => {
    setBranchToDelete(branch);
    setShowConfirmDialog(true);
  };

  const confirmDelete = async () => {
    if (!branchToDelete) return;

    try {
      await branchAPI.delete(branchToDelete.id);
      setBranches(branches.filter(branch => branch.id !== branchToDelete.id));
      setBranchToDelete(null);
    } catch (err) {
      setError('Failed to delete branch');
      console.error('Error deleting branch:', err);
    }
  };

  const cancelDelete = () => {
    setShowConfirmDialog(false);
    setBranchToDelete(null);
  };

  const handleLogout = () => {
    setShowLogoutDialog(true);
  };

  const confirmLogout = () => {
    logout();
    navigate('/login');
  };

  const cancelLogout = () => {
    setShowLogoutDialog(false);
  };

  const handleEdit = (branch: Branch) => {
    setEditingBranch(branch);
    setShowForm(true);
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingBranch(null);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingBranch(null);
    fetchBranches();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-20 h-20 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full animate-pulse mx-auto mb-6 flex items-center justify-center">
            <span className="text-3xl">🔧</span>
          </div>
          <h3 className="text-2xl font-bold text-orange-800 mb-2">Loading Admin Dashboard</h3>
          <p className="text-orange-600">Preparing your management tools...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-100">
      {/* Header */}
      <header className="bg-white shadow-xl border-b-4 border-orange-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div className="flex items-center space-x-6">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg">
                <span className="text-2xl">🛠️</span>
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-orange-600 to-orange-700 bg-clip-text text-transparent">
                  LowCalories Admin
                </h1>
                <p className="text-xl text-orange-700 font-medium">Management Dashboard</p>
                <p className="text-orange-600">Welcome back, {user?.firstName} {user?.lastName}! 👋</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <a
                href="/"
                className="bg-white hover:bg-orange-50 text-orange-600 border-2 border-orange-500 px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
              >
                🌐 Public View
              </a>
              <a
                href="/locations-v2"
                className="bg-orange-100 hover:bg-orange-200 text-orange-700 px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
              >
                🃏 Card View
              </a>
              <button
                onClick={handleLogout}
                className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
              >
                🚪 Logout
              </button>
            </div>
          </div>
        </div>
      </header>



      <main className="max-w-7xl mx-auto py-12 sm:px-6 lg:px-8">
        <div className="px-4 sm:px-0">
          {error && (
            <div className="mb-8 bg-gradient-to-r from-red-50 to-red-100 border-2 border-red-200 text-red-800 px-6 py-4 rounded-2xl shadow-lg">
              <div className="flex items-center">
                <span className="text-2xl mr-3">⚠️</span>
                <div>
                  <h3 className="font-bold text-lg">Error</h3>
                  <p>{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Action Bar */}
          <div className="mb-8 bg-white rounded-2xl shadow-xl p-6 border-2 border-orange-100">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-3xl font-bold text-orange-800 mb-2">🏪 Manage Branches Locations</h2>
                <p className="text-orange-600 text-lg">Add, edit, or remove LowCalories branch locations</p>
              </div>
              <button
                onClick={() => setShowForm(true)}
                className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-4 rounded-xl font-bold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 text-lg"
              >
                ➕ Add New Location
              </button>
            </div>
          </div>

          {/* Branches Grid */}
          {branches.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {branches.map((branch) => (
                <div
                  key={branch.id}
                  className="bg-white rounded-3xl shadow-xl hover:shadow-2xl border-2 border-orange-100 hover:border-orange-300 overflow-hidden transform hover:-translate-y-1 transition-all duration-300"
                >
                  {/* Card Header */}
                  <div className="relative bg-gradient-to-br from-orange-400 to-orange-600 p-6 text-white">
                    <div className="absolute top-4 right-4">
                      <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                        <span className="text-sm font-bold">#{branch.id}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                        <span className="text-2xl">🏪</span>
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold mb-1">{branch.name}</h3>
                        <div className="flex items-center space-x-2">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          <span className="font-medium text-lg">{branch.city}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Card Body */}
                  <div className="p-6">
                    {/* Store Details */}
                    <div className="space-y-4 mb-6">
                      <div>
                        <h4 className="text-sm font-semibold text-orange-800 uppercase tracking-wider mb-2">
                          📍 Address
                        </h4>
                        <p className="text-gray-700 leading-relaxed text-lg">{branch.address}</p>
                      </div>

                      <div>
                        <h4 className="text-sm font-semibold text-orange-800 uppercase tracking-wider mb-2">
                          🗺️ Area/Location
                        </h4>
                        <p className="text-orange-700 font-medium text-lg">{branch.location}</p>
                      </div>

                      <div>
                        <h4 className="text-sm font-semibold text-orange-800 uppercase tracking-wider mb-2">
                          📞 Contact
                        </h4>
                        <a
                          href={`tel:${branch.phone}`}
                          className="text-orange-600 hover:text-orange-800 font-semibold text-lg"
                        >
                          {branch.phone}
                        </a>
                      </div>

                      <div>
                        <h4 className="text-sm font-semibold text-orange-800 uppercase tracking-wider mb-2">
                          📅 Created
                        </h4>
                        <p className="text-gray-600">
                          {new Date(branch.createdAt).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </p>
                      </div>
                    </div>

                    {/* Status Badge */}
                    <div className="mb-6">
                      <div className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-green-100 text-green-800">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                        Active Store
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-3">
                      <button
                        onClick={() => handleEdit(branch)}
                        className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-4 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 flex items-center justify-center space-x-2"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        <span>✏️ Edit</span>
                      </button>

                      <button
                        onClick={() => handleDelete(branch)}
                        className="flex-1 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-semibold py-3 px-4 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 flex items-center justify-center space-x-2"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        <span>🗑️ Delete</span>
                      </button>
                    </div>
                  </div>

                  {/* Card Footer */}
                  <div className="bg-gradient-to-r from-orange-50 to-orange-100 px-6 py-4 border-t border-orange-200">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-orange-800">LowCalories Branch</span>
                      <span className="text-xs text-orange-600">
                        Last updated: {new Date(branch.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            /* No Branches */
            <div className="text-center py-20 bg-white rounded-3xl shadow-xl border-2 border-orange-200">
              <div className="max-w-md mx-auto">
                <div className="w-32 h-32 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full mx-auto mb-8 flex items-center justify-center shadow-2xl">
                  <span className="text-5xl">🏪</span>
                </div>
                <h3 className="text-3xl font-bold text-orange-800 mb-4">No Branches Yet</h3>
                <p className="text-orange-600 mb-6 text-lg">
                  Get started by adding your first LowCalories branch location.
                </p>
                <p className="text-orange-500 mb-8">
                  Click the "Add New Location" button above to begin!
                </p>
                <button
                  onClick={() => setShowForm(true)}
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 text-lg"
                >
                  ➕ Add Your First Branch
                </button>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Branch Form Modal */}
      {showForm && (
        <BranchForm
          branch={editingBranch}
          onClose={handleFormClose}
          onSuccess={handleFormSuccess}
        />
      )}

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        isOpen={showConfirmDialog}
        onClose={cancelDelete}
        onConfirm={confirmDelete}
        title="Delete Branch Location"
        message="Are you sure you want to permanently delete this LowCalories branch location?"
        confirmText="Yes, Delete Branch"
        cancelText="Keep Branch"
        type="danger"
        itemName={branchToDelete?.name}
      />

      {/* Confirm Logout Dialog */}
      <ConfirmDialog
        isOpen={showLogoutDialog}
        onClose={cancelLogout}
        onConfirm={confirmLogout}
        title="Logout Confirmation"
        message="Are you sure you want to logout from the LowCalories Admin Dashboard?"
        confirmText="Yes, Logout"
        cancelText="Stay Logged In"
        type="warning"
      />
    </div>
  );
};

export default AdminDashboard;
