# Contributing to LowCalories Location Management System

Thank you for your interest in contributing to the LowCalories Location Management System! We welcome contributions from the community.

## 🚀 Getting Started

### Prerequisites
- Node.js (v18 or higher)
- .NET 8 SDK
- SQL Server (LocalDB or full instance)
- Git

### Development Setup
1. Fork the repository
2. Clone your fork: `git clone https://github.com/yourusername/lowcalories-locations.git`
3. Install dependencies:
   ```bash
   cd backend && dotnet restore
   cd ../frontend && npm install
   ```
4. Set up the database: `cd backend && dotnet ef database update`
5. Start the development servers:
   ```bash
   # Terminal 1 - Backend
   cd backend && dotnet run
   
   # Terminal 2 - Frontend
   cd frontend && npm run dev
   ```

## 🎯 How to Contribute

### Reporting Bugs
1. Check if the bug has already been reported in [Issues](https://github.com/yourusername/lowcalories-locations/issues)
2. If not, create a new issue with:
   - Clear description of the bug
   - Steps to reproduce
   - Expected vs actual behavior
   - Screenshots if applicable
   - Environment details (OS, browser, etc.)

### Suggesting Features
1. Check existing [Issues](https://github.com/yourusername/lowcalories-locations/issues) for similar suggestions
2. Create a new issue with:
   - Clear description of the feature
   - Use case and benefits
   - Possible implementation approach

### Code Contributions

#### Branch Naming Convention
- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `hotfix/description` - Critical fixes
- `docs/description` - Documentation updates

#### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

Examples:
```
feat(frontend): add location search functionality
fix(backend): resolve authentication token expiration
docs(readme): update installation instructions
```

#### Pull Request Process
1. Create a feature branch from `main`
2. Make your changes
3. Add tests if applicable
4. Update documentation if needed
5. Ensure all tests pass
6. Create a pull request with:
   - Clear title and description
   - Reference to related issues
   - Screenshots for UI changes

## 🧪 Testing

### Frontend Testing
```bash
cd frontend
npm test
```

### Backend Testing
```bash
cd backend
dotnet test
```

## 📝 Code Style

### Frontend (React/TypeScript)
- Use TypeScript for type safety
- Follow React hooks patterns
- Use functional components
- Implement proper error handling
- Use Tailwind CSS for styling

### Backend (ASP.NET Core)
- Follow C# coding conventions
- Use async/await patterns
- Implement proper error handling
- Use Entity Framework best practices
- Add XML documentation for public APIs

## 🔍 Code Review Guidelines

### For Contributors
- Keep changes focused and atomic
- Write clear commit messages
- Add comments for complex logic
- Ensure backward compatibility
- Test thoroughly before submitting

### For Reviewers
- Be constructive and respectful
- Focus on code quality and maintainability
- Check for security issues
- Verify tests are adequate
- Ensure documentation is updated

## 📚 Resources

- [React Documentation](https://reactjs.org/docs)
- [ASP.NET Core Documentation](https://docs.microsoft.com/en-us/aspnet/core/)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

## 🤝 Community Guidelines

- Be respectful and inclusive
- Help others learn and grow
- Share knowledge and best practices
- Follow the [Code of Conduct](CODE_OF_CONDUCT.md)

## 📞 Getting Help

- Create an issue for bugs or feature requests
- Join discussions in existing issues
- Contact maintainers for urgent matters

Thank you for contributing to the LowCalories Location Management System! 🥗✨
