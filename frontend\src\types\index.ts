export interface Branch {
  id: number;
  name: string;
  city: string;
  country: string;
  address: string;
  phone: string;
  location: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  email: string;
  firstName: string;
  lastName: string;
  expiration: string;
}

export interface User {
  email: string;
  firstName: string;
  lastName: string;
  token: string;
}

export interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
}
