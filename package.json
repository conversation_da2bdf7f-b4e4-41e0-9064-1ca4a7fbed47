{"name": "lowcalories-locations", "version": "1.0.0", "description": "A modern, full-stack web application for managing LowCalories restaurant locations across the UAE", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && dotnet run", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "install:all": "npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && dotnet restore", "test": "npm run test:frontend", "test:frontend": "cd frontend && npm test", "lint": "cd frontend && npm run lint", "format": "cd frontend && npm run format"}, "repository": {"type": "git", "url": "git+https://github.com/yourusername/lowcalories-locations.git"}, "keywords": ["lowcalories", "restaurant", "locations", "react", "aspnet-core", "typescript", "tailwind", "sql-server", "entity-framework"], "author": "LowCalories Development Team", "license": "MIT", "bugs": {"url": "https://github.com/yourusername/lowcalories-locations/issues"}, "homepage": "https://github.com/yourusername/lowcalories-locations#readme", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}