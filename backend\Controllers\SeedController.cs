using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using backend.Data;
using backend.Models;

namespace backend.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SeedController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public SeedController(ApplicationDbContext context)
        {
            _context = context;
        }

        // POST: api/seed/branches
        [HttpPost("branches")]
        public async Task<ActionResult> SeedBranches()
        {
            // Check if branches already exist
            var existingBranches = await _context.Branches.CountAsync();
            if (existingBranches > 0)
            {
                return BadRequest(new { message = "Branches already exist. Use /api/seed/branches/force to override." });
            }

            var realLocations = new List<Branch>
            {
                new Branch
                {
                    Name = "LowCalories Dubai Mall",
                    City = "Dubai",
                    Address = "Level 2, Dubai Mall, Downtown Dubai, Dubai, UAE",
                    Phone = "+971 4 325 8900",
                    Location = "Downtown Dubai",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories Marina Walk",
                    City = "Dubai",
                    Address = "Marina Walk, Dubai Marina, Dubai, UAE",
                    Phone = "+971 4 368 7200",
                    Location = "Dubai Marina",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories JBR Beach",
                    City = "Dubai",
                    Address = "The Beach at JBR, Jumeirah Beach Residence, Dubai, UAE",
                    Phone = "+971 4 423 1500",
                    Location = "Jumeirah Beach Residence",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories City Centre Deira",
                    City = "Dubai",
                    Address = "City Centre Deira, Port Saeed, Deira, Dubai, UAE",
                    Phone = "+971 4 295 1010",
                    Location = "Deira",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories Business Bay",
                    City = "Dubai",
                    Address = "Bay Square, Business Bay, Dubai, UAE",
                    Phone = "+971 4 447 8800",
                    Location = "Business Bay",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories Abu Dhabi Mall",
                    City = "Abu Dhabi",
                    Address = "Abu Dhabi Mall, Tourist Club Area, Abu Dhabi, UAE",
                    Phone = "+971 2 645 4000",
                    Location = "Tourist Club Area",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories Yas Mall",
                    City = "Abu Dhabi",
                    Address = "Yas Mall, Yas Island, Abu Dhabi, UAE",
                    Phone = "+971 2 565 8200",
                    Location = "Yas Island",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories Corniche",
                    City = "Abu Dhabi",
                    Address = "Corniche Road, Abu Dhabi, UAE",
                    Phone = "+971 2 681 9900",
                    Location = "Corniche",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories Sharjah City Centre",
                    City = "Sharjah",
                    Address = "Sharjah City Centre, Al Qasimia, Sharjah, UAE",
                    Phone = "+971 6 531 2200",
                    Location = "Al Qasimia",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories Al Wahda",
                    City = "Sharjah",
                    Address = "Al Wahda Street, Al Majaz, Sharjah, UAE",
                    Phone = "+971 6 574 8800",
                    Location = "Al Majaz",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories Ajman City Centre",
                    City = "Ajman",
                    Address = "Ajman City Centre, Sheikh Rashid Bin Saeed Al Maktoum Street, Ajman, UAE",
                    Phone = "+971 6 748 5500",
                    Location = "City Centre",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories RAK Mall",
                    City = "Ras Al Khaimah",
                    Address = "RAK Mall, Al Nakheel, Ras Al Khaimah, UAE",
                    Phone = "+971 7 228 8800",
                    Location = "Al Nakheel",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories Fujairah City Centre",
                    City = "Fujairah",
                    Address = "Fujairah City Centre, Fujairah, UAE",
                    Phone = "+971 9 223 4400",
                    Location = "City Centre",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories Al Ain Mall",
                    City = "Al Ain",
                    Address = "Al Ain Mall, Othman Bin Affan Street, Al Ain, UAE",
                    Phone = "+971 3 766 8800",
                    Location = "Al Ain Mall",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Branch
                {
                    Name = "LowCalories Umm Al Quwain",
                    City = "Umm Al Quwain",
                    Address = "King Faisal Street, Umm Al Quwain, UAE",
                    Phone = "+971 6 766 2200",
                    Location = "King Faisal Street",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            _context.Branches.AddRange(realLocations);
            await _context.SaveChangesAsync();

            return Ok(new {
                message = "Successfully seeded database with LowCalories locations!",
                count = realLocations.Count,
                locations = realLocations.Select(b => new { b.Id, b.Name, b.City }).ToList()
            });
        }

        // POST: api/seed/branches/force
        [HttpPost("branches/force")]
        public async Task<ActionResult> SeedBranchesForce()
        {
            // Clear existing branches
            var existingBranches = await _context.Branches.ToListAsync();
            _context.Branches.RemoveRange(existingBranches);
            await _context.SaveChangesAsync();

            // Call the regular seed method
            return await SeedBranches();
        }

        // GET: api/seed/status
        [HttpGet("status")]
        public async Task<ActionResult> GetSeedStatus()
        {
            var branchCount = await _context.Branches.CountAsync();
            var cities = await _context.Branches.Select(b => b.City).Distinct().ToListAsync();

            return Ok(new {
                branchCount,
                cities,
                isSeeded = branchCount > 0,
                message = branchCount > 0 ? "Database has been seeded" : "Database is empty"
            });
        }

        // DELETE: api/seed/clear
        [HttpDelete("clear")]
        public async Task<ActionResult> ClearAllData()
        {
            var branches = await _context.Branches.ToListAsync();
            _context.Branches.RemoveRange(branches);
            await _context.SaveChangesAsync();

            return Ok(new {
                message = "All branch data cleared successfully!",
                deletedCount = branches.Count
            });
        }
    }
}
