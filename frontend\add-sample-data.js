// Script to add real LowCalories location data
const API_BASE_URL = 'http://localhost:5170/api';

const realLocations = [
  {
    name: "LowCalories Dubai Mall",
    city: "Dubai",
    address: "Level 2, Dubai Mall, Downtown Dubai, Dubai, UAE",
    phone: "+971 4 325 8900"
  },
  {
    name: "LowCalories Marina Walk",
    city: "Dubai",
    address: "Marina Walk, Dubai Marina, Dubai, UAE",
    phone: "+971 4 368 7200"
  },
  {
    name: "LowCalories JBR Beach",
    city: "Dubai",
    address: "The Beach at JBR, Jumeirah Beach Residence, Dubai, UAE",
    phone: "+971 4 423 1500"
  },
  {
    name: "LowCalories City Centre Deira",
    city: "Dubai",
    address: "City Centre Deira, Port Saeed, Deira, Dubai, UAE",
    phone: "+971 4 295 1010"
  },
  {
    name: "LowCalories Business Bay",
    city: "Dubai",
    address: "Bay Square, Business Bay, Dubai, UAE",
    phone: "+971 4 447 8800"
  },
  {
    name: "LowCalories Abu Dhabi Mall",
    city: "Abu Dhabi",
    address: "Abu Dhabi Mall, Tourist Club Area, Abu Dhabi, UAE",
    phone: "+971 2 645 4000"
  },
  {
    name: "LowCalories Yas Mall",
    city: "Abu Dhabi",
    address: "Yas Mall, Yas Island, Abu Dhabi, UAE",
    phone: "+971 2 565 8200"
  },
  {
    name: "LowCalories Corniche",
    city: "Abu Dhabi",
    address: "Corniche Road, Abu Dhabi, UAE",
    phone: "+971 2 681 9900"
  },
  {
    name: "LowCalories Sharjah City Centre",
    city: "Sharjah",
    address: "Sharjah City Centre, Al Qasimia, Sharjah, UAE",
    phone: "+971 6 531 2200"
  },
  {
    name: "LowCalories Al Wahda",
    city: "Sharjah",
    address: "Al Wahda Street, Al Majaz, Sharjah, UAE",
    phone: "+971 6 574 8800"
  },
  {
    name: "LowCalories Ajman City Centre",
    city: "Ajman",
    address: "Ajman City Centre, Sheikh Rashid Bin Saeed Al Maktoum Street, Ajman, UAE",
    phone: "+971 6 748 5500"
  },
  {
    name: "LowCalories RAK Mall",
    city: "Ras Al Khaimah",
    address: "RAK Mall, Al Nakheel, Ras Al Khaimah, UAE",
    phone: "+971 7 228 8800"
  },
  {
    name: "LowCalories Fujairah City Centre",
    city: "Fujairah",
    address: "Fujairah City Centre, Fujairah, UAE",
    phone: "+971 9 223 4400"
  },
  {
    name: "LowCalories Al Ain Mall",
    city: "Al Ain",
    address: "Al Ain Mall, Othman Bin Affan Street, Al Ain, UAE",
    phone: "+971 3 766 8800"
  },
  {
    name: "LowCalories Umm Al Quwain",
    city: "Umm Al Quwain",
    address: "King Faisal Street, Umm Al Quwain, UAE",
    phone: "+971 6 766 2200"
  }
];

async function addLocation(location) {
  try {
    const response = await fetch(`${API_BASE_URL}/branches`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(location),
    });

    if (response.ok) {
      const result = await response.json();
      console.log(`✅ Added: ${location.name} in ${location.city}`);
      return result;
    } else {
      console.error(`❌ Failed to add: ${location.name} - Status: ${response.status}`);
      const errorText = await response.text();
      console.error('Error details:', errorText);
    }
  } catch (error) {
    console.error(`❌ Error adding ${location.name}:`, error.message);
  }
}

async function addAllLocations() {
  console.log('🚀 Starting to add LowCalories locations...\n');
  
  for (let i = 0; i < realLocations.length; i++) {
    const location = realLocations[i];
    console.log(`📍 Adding location ${i + 1}/${realLocations.length}: ${location.name}`);
    await addLocation(location);
    
    // Add a small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n🎉 Finished adding all locations!');
  console.log('🌐 You can now view them at: http://localhost:5173/');
  console.log('🃏 Or in card view at: http://localhost:5173/locations-v2');
  console.log('🛠️ Manage them at: http://localhost:5173/admin');
}

// Run the script
addAllLocations().catch(console.error);
