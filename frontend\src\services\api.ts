import axios from 'axios';
import { Branch, LoginRequest, LoginResponse } from '../types';

const API_BASE_URL = 'http://localhost:5170/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const authAPI = {
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },
};

export const branchAPI = {
  getAll: async (): Promise<Branch[]> => {
    const response = await api.get('/branches');
    return response.data;
  },

  getById: async (id: number): Promise<Branch> => {
    const response = await api.get(`/branches/${id}`);
    return response.data;
  },

  create: async (branch: Omit<Branch, 'id' | 'createdAt' | 'updatedAt'>): Promise<Branch> => {
    const response = await api.post('/branches', branch);
    return response.data;
  },

  update: async (id: number, branch: Omit<Branch, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> => {
    await api.put(`/branches/${id}`, { ...branch, id });
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/branches/${id}`);
  },
};

export default api;
