# 🥗 LowCalories Location Management System

A modern, full-stack web application for managing LowCalories restaurant locations across the UAE. Built with React, ASP.NET Core, and SQL Server.

![LowCalories Logo](https://lowcalories.ae/assets/images/logo.png)

## 🌟 Features

### 📍 Public Location Directory
- **Beautiful Location Display** - Modern table and card views of all LowCalories branches
- **Advanced Search & Filtering** - Search by name, address, phone, or filter by city
- **Responsive Design** - Works perfectly on desktop, tablet, and mobile devices
- **LowCalories Branding** - Consistent orange theme matching the official website

### 🔐 Admin Dashboard
- **Secure Authentication** - Microsoft Identity integration with role-based access
- **Full CRUD Operations** - Add, edit, delete, and manage branch locations
- **Professional Interface** - Modern admin dashboard with enhanced UX
- **Logout Confirmation** - Beautiful confirmation dialogs for better user experience

### 🎨 Modern UI/UX
- **Tailwind CSS** - Utility-first CSS framework for rapid development
- **Gradient Designs** - Beautiful orange gradients matching LowCalories branding
- **Smooth Animations** - Hover effects, transitions, and micro-interactions
- **Professional Typography** - Clean, readable fonts and spacing

## 🚀 Tech Stack

### Frontend
- **React 18** - Modern React with hooks and functional components
- **TypeScript** - Type-safe development
- **Vite** - Lightning-fast development server and build tool
- **React Router** - Client-side routing with protected routes
- **Tailwind CSS** - Utility-first CSS framework

### Backend
- **ASP.NET Core 8** - Modern .NET web API
- **Entity Framework Core** - ORM for database operations
- **Microsoft Identity** - Authentication and authorization
- **SQL Server** - Robust relational database

### Database
- **SQL Server** - Production-ready database
- **Entity Framework Migrations** - Database schema management
- **Seeded Data** - Pre-populated with 15 LowCalories locations

## 📦 Installation & Setup

### Prerequisites
- **Node.js** (v18 or higher)
- **.NET 8 SDK**
- **SQL Server** (LocalDB or full instance)
- **Git**

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/lowcalories-locations.git
cd lowcalories-locations
```

### 2. Backend Setup
```bash
cd backend
dotnet restore
dotnet ef database update
dotnet run
```
The API will be available at `http://localhost:5170`

### 3. Frontend Setup
```bash
cd frontend
npm install
npm run dev
```
The web app will be available at `http://localhost:5173`

## 🌐 Application URLs

### Public Pages
- **Main Locations**: `http://localhost:5173/`
- **Card View**: `http://localhost:5173/locations-v2`

### Admin Pages
- **Login**: `http://localhost:5173/login`
- **Dashboard**: `http://localhost:5173/admin`

## 🔑 Demo Credentials

For testing the admin functionality:
- **Email**: `<EMAIL>`
- **Password**: `Admin123!`

## 📱 Screenshots

### Public Location Directory
Beautiful, responsive table view with search and filtering capabilities.

### Admin Dashboard
Professional admin interface for managing branch locations.

### Mobile Responsive
Optimized for all screen sizes and devices.

## 🗂️ Project Structure

```
lowcalories-locations/
├── backend/                 # ASP.NET Core API
│   ├── Controllers/         # API controllers
│   ├── Models/             # Data models
│   ├── Data/               # Database context
│   └── Program.cs          # Application entry point
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── contexts/       # React contexts
│   │   ├── services/       # API services
│   │   └── types/          # TypeScript types
│   ├── public/             # Static assets
│   └── package.json        # Dependencies
└── README.md               # This file
```

## 🔧 Configuration

### Database Connection
Update the connection string in `backend/appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=MSI;Database=LocationsDB;User Id=sa;Password=***;TrustServerCertificate=true;"
  }
}
```

### API Base URL
Update the API URL in `frontend/src/services/api.ts` if needed:
```typescript
const API_BASE_URL = 'http://localhost:5170/api';
```

## 🚀 Deployment

### Backend Deployment
1. Publish the ASP.NET Core application
2. Configure production database connection
3. Set up authentication providers
4. Deploy to Azure App Service or similar

### Frontend Deployment
1. Build the React application: `npm run build`
2. Deploy to Netlify, Vercel, or Azure Static Web Apps
3. Update API base URL for production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **LowCalories** - For the inspiration and branding
- **React Team** - For the amazing frontend framework
- **Microsoft** - For the robust .NET ecosystem
- **Tailwind CSS** - For the beautiful utility-first CSS framework

## 📞 Support

For support or questions, please open an issue on GitHub or contact the development team.

---

**Built with ❤️ for LowCalories** - Healthy meals, beautiful locations! 🥗✨
