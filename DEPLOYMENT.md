# 🚀 Deployment Guide

This guide covers deploying the LowCalories Location Management System to various platforms.

## 📋 Prerequisites

- Git repository with your code
- Production database (Azure SQL, AWS RDS, etc.)
- Domain name (optional)
- SSL certificate (recommended)

## 🌐 Frontend Deployment

### Netlify (Recommended)

1. **Build the frontend**:
   ```bash
   cd frontend
   npm run build
   ```

2. **Deploy to Netlify**:
   - Connect your GitHub repository to Netlify
   - Set build command: `cd frontend && npm run build`
   - Set publish directory: `frontend/dist`
   - Add environment variables:
     ```
     VITE_API_BASE_URL=https://your-api-domain.com/api
     ```

3. **Configure redirects** (create `frontend/public/_redirects`):
   ```
   /*    /index.html   200
   ```

### Vercel

1. **Install Vercel CLI**:
   ```bash
   npm install -g vercel
   ```

2. **Deploy**:
   ```bash
   cd frontend
   vercel --prod
   ```

3. **Configure environment variables** in Vercel dashboard:
   ```
   VITE_API_BASE_URL=https://your-api-domain.com/api
   ```

### Azure Static Web Apps

1. **Create Azure Static Web App**
2. **Connect GitHub repository**
3. **Configure build settings**:
   - App location: `/frontend`
   - Build location: `/frontend/dist`
   - Build command: `npm run build`

## 🖥️ Backend Deployment

### Azure App Service (Recommended)

1. **Prepare for deployment**:
   ```bash
   cd backend
   dotnet publish -c Release -o ./publish
   ```

2. **Create Azure App Service**:
   - Runtime: .NET 8
   - Operating System: Windows or Linux

3. **Configure connection strings**:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=your-server.database.windows.net;Database=LocationsDB;User Id=your-username;Password=your-password;Encrypt=true;"
     }
   }
   ```

4. **Deploy using Azure CLI**:
   ```bash
   az webapp deployment source config-zip \
     --resource-group your-resource-group \
     --name your-app-name \
     --src publish.zip
   ```

### Docker Deployment

1. **Create Dockerfile** (in backend directory):
   ```dockerfile
   FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
   WORKDIR /app
   EXPOSE 80
   EXPOSE 443

   FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
   WORKDIR /src
   COPY ["backend.csproj", "."]
   RUN dotnet restore "./backend.csproj"
   COPY . .
   WORKDIR "/src/."
   RUN dotnet build "backend.csproj" -c Release -o /app/build

   FROM build AS publish
   RUN dotnet publish "backend.csproj" -c Release -o /app/publish

   FROM base AS final
   WORKDIR /app
   COPY --from=publish /app/publish .
   ENTRYPOINT ["dotnet", "backend.dll"]
   ```

2. **Build and run**:
   ```bash
   docker build -t lowcalories-api .
   docker run -p 8080:80 lowcalories-api
   ```

### Railway

1. **Connect GitHub repository**
2. **Configure environment variables**:
   ```
   ConnectionStrings__DefaultConnection=your-database-connection-string
   ASPNETCORE_ENVIRONMENT=Production
   ```

## 🗄️ Database Deployment

### Azure SQL Database

1. **Create Azure SQL Database**
2. **Configure firewall rules**
3. **Run migrations**:
   ```bash
   dotnet ef database update --connection "your-connection-string"
   ```

### PostgreSQL (Alternative)

1. **Update Entity Framework packages**:
   ```bash
   dotnet add package Npgsql.EntityFrameworkCore.PostgreSQL
   ```

2. **Update DbContext configuration**:
   ```csharp
   services.AddDbContext<ApplicationDbContext>(options =>
       options.UseNpgsql(connectionString));
   ```

## 🔧 Environment Configuration

### Production Environment Variables

**Frontend (.env.production)**:
```
VITE_API_BASE_URL=https://your-api-domain.com/api
VITE_APP_NAME=LowCalories Locations
```

**Backend (appsettings.Production.json)**:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "your-production-connection-string"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning"
    }
  },
  "AllowedHosts": "*",
  "Cors": {
    "AllowedOrigins": ["https://your-frontend-domain.com"]
  }
}
```

## 🔒 Security Considerations

### HTTPS Configuration
- Enable HTTPS in production
- Use SSL certificates (Let's Encrypt for free certificates)
- Configure HSTS headers

### CORS Configuration
```csharp
services.AddCors(options =>
{
    options.AddPolicy("Production", builder =>
    {
        builder.WithOrigins("https://your-frontend-domain.com")
               .AllowAnyMethod()
               .AllowAnyHeader()
               .AllowCredentials();
    });
});
```

### Authentication
- Use strong JWT secrets
- Configure proper token expiration
- Enable two-factor authentication if needed

## 📊 Monitoring & Logging

### Application Insights (Azure)
```csharp
services.AddApplicationInsightsTelemetry();
```

### Logging Configuration
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

## 🔄 CI/CD Pipeline

### GitHub Actions Example

**.github/workflows/deploy.yml**:
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: cd frontend && npm install
      - name: Build
        run: cd frontend && npm run build
      - name: Deploy to Netlify
        uses: netlify/actions/cli@master
        with:
          args: deploy --prod --dir=frontend/dist
        env:
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}

  deploy-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0'
      - name: Restore dependencies
        run: cd backend && dotnet restore
      - name: Build
        run: cd backend && dotnet build --no-restore
      - name: Publish
        run: cd backend && dotnet publish -c Release -o ./publish
      - name: Deploy to Azure
        uses: azure/webapps-deploy@v2
        with:
          app-name: 'your-app-name'
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
          package: backend/publish
```

## 🧪 Production Testing

### Health Checks
```csharp
services.AddHealthChecks()
    .AddDbContext<ApplicationDbContext>();

app.MapHealthChecks("/health");
```

### Load Testing
- Use tools like Artillery, JMeter, or Azure Load Testing
- Test API endpoints under load
- Monitor database performance

## 📈 Performance Optimization

### Frontend
- Enable gzip compression
- Use CDN for static assets
- Implement code splitting
- Optimize images

### Backend
- Enable response compression
- Use database connection pooling
- Implement caching strategies
- Optimize database queries

## 🆘 Troubleshooting

### Common Issues
1. **CORS errors**: Check allowed origins configuration
2. **Database connection**: Verify connection string and firewall rules
3. **Authentication issues**: Check JWT configuration and secrets
4. **Build failures**: Verify Node.js and .NET versions

### Monitoring
- Set up application monitoring
- Configure error tracking
- Monitor database performance
- Set up alerts for critical issues

---

For additional help with deployment, please check the [Contributing Guide](CONTRIBUTING.md) or create an issue on GitHub.
