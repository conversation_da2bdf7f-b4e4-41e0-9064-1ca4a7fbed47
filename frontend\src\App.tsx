import React from 'react';
import { BrowserRouter as Router, Routes, Route, useNavigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import AdminLogin from './components/Login';
import AdminDashboard from './components/AdminDashboard';
import ProtectedRoute from './components/ProtectedRoute';
import LocationsV2 from './components/LocationsV2';

interface Branch {
  id: number;
  name: string;
  city: string;
  address: string;
  phone: string;
  location: string;
  createdAt: string;
  updatedAt: string;
}

// Main Locations Page Component
function LocationsPage() {
  const [branches, setBranches] = React.useState<Branch[]>([]);
  const [filteredBranches, setFilteredBranches] = React.useState<Branch[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [searchTerm, setSearchTerm] = React.useState('');
  const [cityFilter, setCityFilter] = React.useState('');
  const navigate = useNavigate();

  React.useEffect(() => {
    fetchBranches();
  }, []);

  React.useEffect(() => {
    filterBranches();
  }, [branches, searchTerm, cityFilter]);

  const fetchBranches = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('http://localhost:5170/api/branches');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setBranches(data);
      setError(null);
    } catch (err: any) {
      console.error('Error fetching branches:', err);
      setError(`Failed to fetch branches: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const filterBranches = () => {
    let filtered = branches;

    if (searchTerm) {
      filtered = filtered.filter(branch =>
        branch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.phone.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (cityFilter) {
      filtered = filtered.filter(branch => branch.city === cityFilter);
    }

    setFilteredBranches(filtered);
  };

  const uniqueCities = Array.from(new Set(branches.map(branch => branch.city))).sort();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">Loading locations...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white border border-red-200 text-red-700 px-8 py-6 rounded-xl max-w-md shadow-lg">
          <div className="flex items-center mb-4">
            <svg className="w-6 h-6 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="font-bold text-lg">Error Loading Data</h3>
          </div>
          <p className="mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-100">
      {/* Header */}
      <header className="bg-white shadow-lg border-b-4 border-orange-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-24">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <img
                  src="https://lowcalories.ae/assets/images/logo.png"
                  alt="LowCalories Logo"
                  className="h-16 w-auto"
                  onError={(e) => {
                    // Fallback if logo doesn't load
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.nextElementSibling.style.display = 'flex';
                  }}
                />
                <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-orange-600 rounded-2xl items-center justify-center shadow-lg hidden">
                  <div className="text-white font-bold text-xl">LC</div>
                </div>
              </div>
              <div className="ml-6">
                <h1 className="text-3xl font-bold bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent">
                  LowCalories
                </h1>
                <p className="text-lg font-medium text-orange-700">Branches Locations</p>
                <p className="text-sm text-orange-600">Find healthy meals near you</p>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-6">
              <div className="text-center">
                <span className="block text-2xl font-bold text-orange-600">{branches.length}</span>
                <span className="text-sm text-orange-500 font-medium">Locations</span>
              </div>
              <button
                onClick={() => navigate('/locations-v2')}
                className="bg-white hover:bg-orange-50 text-orange-600 border-2 border-orange-500 px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
              >
                Card View
              </button>
              <button
                onClick={() => navigate('/login')}
                className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
              >
                Admin Login
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Search Section */}
      <div className="bg-gradient-to-r from-orange-500 to-orange-600 border-b-4 border-orange-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl font-bold text-white mb-3">
              🥗 Find Your Nearest LowCalories Branch
            </h2>
            <p className="text-orange-100 mb-8 text-lg">
              Discover healthy, delicious meals at our convenient locations
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <svg className="h-6 w-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    placeholder="Search by branch name, address, or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="block w-full pl-12 pr-4 py-4 border-2 border-orange-300 rounded-xl focus:ring-4 focus:ring-orange-200 focus:border-orange-400 text-lg font-medium placeholder-orange-400 bg-white/95 backdrop-blur-sm"
                  />
                </div>
              </div>
              <div className="sm:w-56">
                <select
                  value={cityFilter}
                  onChange={(e) => setCityFilter(e.target.value)}
                  className="block w-full px-4 py-4 border-2 border-orange-300 rounded-xl focus:ring-4 focus:ring-orange-200 focus:border-orange-400 text-lg font-medium bg-white/95 backdrop-blur-sm"
                >
                  <option value="">🏙️ All Cities</option>
                  {uniqueCities.map(city => (
                    <option key={city} value={city}>📍 {city}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Results Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Results Summary */}
        <div className="mb-8 flex items-center justify-between bg-white rounded-2xl shadow-lg p-6 border-l-4 border-orange-500">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div>
              <p className="text-2xl font-bold text-orange-600">
                {filteredBranches.length} of {branches.length}
              </p>
              <p className="text-orange-700 font-medium">LowCalories Locations Found</p>
            </div>
          </div>
          {(searchTerm || cityFilter) && (
            <button
              onClick={() => {
                setSearchTerm('');
                setCityFilter('');
              }}
              className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
            >
              🔄 Clear Filters
            </button>
          )}
        </div>

        {/* Locations Table */}
        <div className="bg-white shadow-2xl rounded-2xl overflow-hidden border-2 border-orange-100">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-orange-200">
              <thead className="bg-gradient-to-r from-orange-500 to-orange-600">
                <tr>
                  <th className="px-8 py-6 text-left text-sm font-bold text-white uppercase tracking-wider">
                    🏪 Branch Information
                  </th>
                  <th className="px-8 py-6 text-left text-sm font-bold text-white uppercase tracking-wider">
                    📍 Location Details
                  </th>
                  <th className="px-8 py-6 text-left text-sm font-bold text-white uppercase tracking-wider">
                    🗺️ Area/Location
                  </th>
                  <th className="px-8 py-6 text-left text-sm font-bold text-white uppercase tracking-wider">
                    📞 Contact Info
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-orange-100">
                {filteredBranches.map((branch, index) => (
                  <tr key={branch.id} className="hover:bg-gradient-to-r hover:from-orange-50 hover:to-orange-100 transition-all duration-200 transform hover:scale-[1.01]">
                    <td className="px-8 py-6">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-14 w-14">
                          <div className="h-14 w-14 rounded-2xl bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center shadow-lg">
                            <span className="text-white font-bold text-lg">🥗</span>
                          </div>
                        </div>
                        <div className="ml-6">
                          <div className="text-lg font-bold text-orange-800">{branch.name}</div>
                          <div className="text-sm text-orange-600 font-medium">LowCalories Branch #{branch.id}</div>
                          <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-orange-100 text-orange-800 mt-1">
                            ✅ Healthy Meals Available
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-8 py-6">
                      <div className="space-y-2">
                        <div className="flex items-center text-orange-800 font-semibold">
                          <svg className="w-4 h-4 mr-2 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          {branch.city}
                        </div>
                        <div className="text-orange-700 leading-relaxed">{branch.address}</div>
                      </div>
                    </td>
                    <td className="px-8 py-6">
                      <div className="space-y-2">
                        <div className="flex items-center text-orange-800 font-semibold">
                          <svg className="w-4 h-4 mr-2 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3" />
                          </svg>
                          {branch.location}
                        </div>
                        <div className="text-orange-600 text-sm">Specific Area</div>
                      </div>
                    </td>
                    <td className="px-8 py-6">
                      <div className="space-y-2">
                        <a
                          href={`tel:${branch.phone}`}
                          className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
                        >
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                          </svg>
                          📞 {branch.phone}
                        </a>
                        <div className="text-xs text-orange-600 font-medium">
                          Tap to call for orders & info
                        </div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* No Results */}
        {filteredBranches.length === 0 && (
          <div className="text-center py-16 bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl shadow-xl border-2 border-orange-200">
            <div className="w-24 h-24 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full mx-auto mb-6 flex items-center justify-center shadow-lg">
              <span className="text-4xl">🔍</span>
            </div>
            <h3 className="text-2xl font-bold text-orange-800 mb-3">No LowCalories Branches Found</h3>
            <p className="text-orange-600 mb-6 text-lg">We couldn't find any branches matching your search criteria.</p>
            <p className="text-orange-500 mb-8">Try adjusting your search or browse all our healthy locations!</p>
            <button
              onClick={() => {
                setSearchTerm('');
                setCityFilter('');
              }}
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 text-lg"
            >
              🥗 Show All LowCalories Locations
            </button>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-gradient-to-r from-orange-600 to-orange-700 border-t-4 border-orange-800 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex items-center justify-center mb-6">
              <img
                src="https://lowcalories.ae/assets/images/logo.png"
                alt="LowCalories Logo"
                className="h-10 w-auto mr-4"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling.style.display = 'flex';
                }}
              />
              <div className="w-10 h-10 bg-white rounded-xl items-center justify-center mr-4 hidden">
                <span className="text-xl">🥗</span>
              </div>
              <h3 className="text-2xl font-bold text-white">LowCalories</h3>
            </div>
            <p className="text-orange-100 text-lg mb-4">
              Healthy, delicious meals delivered fresh to your neighborhood
            </p>
            <p className="text-orange-200 text-sm mb-2">
              &copy; 2025 LowCalories. All rights reserved.
            </p>
            <p className="text-orange-300 text-xs">
              🌱 Committed to your health • Last updated: {new Date().toLocaleDateString()}
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

// Main App Component with Routing
function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          <Route path="/" element={<LocationsPage />} />
          <Route path="/locations-v2" element={<LocationsV2 />} />
          <Route path="/login" element={<AdminLogin />} />
          <Route
            path="/admin"
            element={
              <ProtectedRoute>
                <AdminDashboard />
              </ProtectedRoute>
            }
          />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
