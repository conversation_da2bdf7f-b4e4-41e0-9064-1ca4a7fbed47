import React from 'react';

interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info';
  itemName?: string;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  type = 'danger',
  itemName
}) => {
  if (!isOpen) return null;

  const getTypeStyles = () => {
    switch (type) {
      case 'danger':
        return {
          icon: '🗑️',
          iconBg: 'bg-gradient-to-br from-red-500 to-red-600',
          confirmBtn: 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',
          titleColor: 'text-red-800',
          messageColor: 'text-red-700'
        };
      case 'warning':
        return {
          icon: '⚠️',
          iconBg: 'bg-gradient-to-br from-yellow-500 to-yellow-600',
          confirmBtn: 'bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700',
          titleColor: 'text-yellow-800',
          messageColor: 'text-yellow-700'
        };
      case 'info':
        return {
          icon: 'ℹ️',
          iconBg: 'bg-gradient-to-br from-blue-500 to-blue-600',
          confirmBtn: 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',
          titleColor: 'text-blue-800',
          messageColor: 'text-blue-700'
        };
      default:
        return {
          icon: '🗑️',
          iconBg: 'bg-gradient-to-br from-red-500 to-red-600',
          confirmBtn: 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',
          titleColor: 'text-red-800',
          messageColor: 'text-red-700'
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300"
        onClick={onClose}
      ></div>
      
      {/* Dialog */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-3xl shadow-2xl max-w-md w-full mx-auto transform transition-all duration-300 scale-100">
          {/* Header */}
          <div className="p-8 text-center">
            {/* Icon */}
            <div className={`w-20 h-20 ${styles.iconBg} rounded-full mx-auto mb-6 flex items-center justify-center shadow-xl`}>
              <span className="text-3xl text-white">{styles.icon}</span>
            </div>
            
            {/* Title */}
            <h3 className={`text-2xl font-bold ${styles.titleColor} mb-4`}>
              {title}
            </h3>
            
            {/* Message */}
            <div className="space-y-3">
              <p className={`text-lg ${styles.messageColor} leading-relaxed`}>
                {message}
              </p>
              
              {itemName && (
                <div className="bg-gradient-to-r from-orange-50 to-orange-100 border-2 border-orange-200 rounded-xl p-4 mt-4">
                  <div className="flex items-center justify-center space-x-2">
                    <span className="text-lg">🏪</span>
                    <span className="font-bold text-orange-800 text-lg">{itemName}</span>
                  </div>
                </div>
              )}
              
              <p className="text-gray-600 text-sm mt-4">
                This action cannot be undone.
              </p>
            </div>
          </div>
          
          {/* Actions */}
          <div className="bg-gray-50 px-8 py-6 rounded-b-3xl flex space-x-4">
            <button
              onClick={onClose}
              className="flex-1 bg-white hover:bg-gray-50 text-gray-700 border-2 border-gray-300 hover:border-gray-400 font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
            >
              {cancelText}
            </button>
            
            <button
              onClick={() => {
                onConfirm();
                onClose();
              }}
              className={`flex-1 ${styles.confirmBtn} text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200`}
            >
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmDialog;
