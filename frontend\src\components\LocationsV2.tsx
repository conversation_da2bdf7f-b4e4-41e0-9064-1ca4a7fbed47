import React from 'react';
import { useNavigate } from 'react-router-dom';

interface Branch {
  id: number;
  name: string;
  city: string;
  address: string;
  phone: string;
  location: string;
  createdAt: string;
  updatedAt: string;
}

function LocationsV2() {
  const [branches, setBranches] = React.useState<Branch[]>([]);
  const [filteredBranches, setFilteredBranches] = React.useState<Branch[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [searchTerm, setSearchTerm] = React.useState('');
  const [cityFilter, setCityFilter] = React.useState('');
  const navigate = useNavigate();

  React.useEffect(() => {
    fetchBranches();
  }, []);

  React.useEffect(() => {
    filterBranches();
  }, [branches, searchTerm, cityFilter]);

  const fetchBranches = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('http://localhost:5170/api/branches');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setBranches(data);
      setError(null);
    } catch (err: any) {
      console.error('Error fetching branches:', err);
      setError(`Failed to fetch branches: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const filterBranches = () => {
    let filtered = branches;

    if (searchTerm) {
      filtered = filtered.filter(branch =>
        branch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.phone.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (cityFilter) {
      filtered = filtered.filter(branch => branch.city === cityFilter);
    }

    setFilteredBranches(filtered);
  };

  const uniqueCities = Array.from(new Set(branches.map(branch => branch.city))).sort();

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-20 h-20 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full animate-pulse mx-auto mb-6 flex items-center justify-center">
            <span className="text-3xl">🥗</span>
          </div>
          <h3 className="text-2xl font-bold text-orange-800 mb-2">Loading LowCalories Locations</h3>
          <p className="text-orange-600">Finding healthy meals near you...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-100 flex items-center justify-center">
        <div className="bg-white border-2 border-orange-200 text-orange-800 px-8 py-6 rounded-2xl max-w-md shadow-2xl">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mr-4">
              <span className="text-2xl">⚠️</span>
            </div>
            <h3 className="font-bold text-xl">Oops! Something went wrong</h3>
          </div>
          <p className="mb-6 text-orange-700">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg transition-all duration-200"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-100">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-orange-500 to-orange-600 overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <div className="flex items-center justify-center mb-8">
              <img
                src="https://lowcalories.ae/assets/images/logo.png"
                alt="LowCalories Logo"
                className="h-20 w-auto mr-6"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling.style.display = 'flex';
                }}
              />
              <div className="w-20 h-20 bg-white rounded-2xl items-center justify-center mr-6 hidden">
                <span className="text-3xl">🥗</span>
              </div>
              <div className="text-left">
                <h1 className="text-5xl font-bold text-white mb-2">LowCalories</h1>
                <p className="text-xl text-orange-100 font-medium">Branches Locations</p>
              </div>
            </div>
            <h2 className="text-3xl font-bold text-white mb-4">
              🌟 Discover Our Locations
            </h2>
            <p className="text-xl text-orange-100 mb-8 max-w-3xl mx-auto">
              Find the nearest LowCalories branch and enjoy fresh, healthy, and delicious meals crafted with love
            </p>

            {/* Search Bar in Hero */}
            <div className="max-w-2xl mx-auto">
              <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-2xl">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <svg className="h-6 w-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                      <input
                        type="text"
                        placeholder="Search branches, addresses, or phone numbers..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="block w-full pl-12 pr-4 py-4 border-2 border-orange-200 rounded-xl focus:ring-4 focus:ring-orange-200 focus:border-orange-400 text-lg font-medium placeholder-orange-400"
                      />
                    </div>
                  </div>
                  <div className="sm:w-56">
                    <select
                      value={cityFilter}
                      onChange={(e) => setCityFilter(e.target.value)}
                      className="block w-full px-4 py-4 border-2 border-orange-200 rounded-xl focus:ring-4 focus:ring-orange-200 focus:border-orange-400 text-lg font-medium"
                    >
                      <option value="">🏙️ All Cities</option>
                      {uniqueCities.map(city => (
                        <option key={city} value={city}>📍 {city}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="absolute top-6 right-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/')}
              className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-xl font-medium backdrop-blur-sm transition-all duration-200"
            >
              Table View
            </button>
            <button
              onClick={() => navigate('/admin/login')}
              className="bg-white hover:bg-orange-50 text-orange-600 px-6 py-2 rounded-xl font-semibold shadow-lg transition-all duration-200"
            >
              Admin Login
            </button>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-orange-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl">🏪</span>
              </div>
              <h3 className="text-3xl font-bold text-orange-600 mb-2">{branches.length}</h3>
              <p className="text-orange-800 font-medium">Branches Locations</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-orange-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl">🌟</span>
              </div>
              <h3 className="text-3xl font-bold text-orange-600 mb-2">100%</h3>
              <p className="text-orange-800 font-medium">Fresh & Healthy</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-orange-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl">⚡</span>
              </div>
              <h3 className="text-3xl font-bold text-orange-600 mb-2">Fast</h3>
              <p className="text-orange-800 font-medium">Quick Service</p>
            </div>
          </div>
        </div>
      </section>

      {/* Results Summary */}
      <section className="py-8 bg-gradient-to-r from-orange-100 to-orange-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">{filteredBranches.length}</span>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-orange-800">
                  {filteredBranches.length} Location{filteredBranches.length !== 1 ? 's' : ''} Found
                </h3>
                <p className="text-orange-600">Ready to serve you healthy meals</p>
              </div>
            </div>
            {(searchTerm || cityFilter) && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setCityFilter('');
                }}
                className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
              >
                🔄 Clear Filters
              </button>
            )}
          </div>
        </div>
      </section>

      {/* Locations Cards Grid */}
      <main className="py-16 bg-gradient-to-br from-orange-50 via-white to-orange-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {filteredBranches.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredBranches.map((branch, index) => (
                <div
                  key={branch.id}
                  className="group bg-white rounded-3xl shadow-xl hover:shadow-2xl border-2 border-orange-100 hover:border-orange-300 overflow-hidden transform hover:-translate-y-2 transition-all duration-300"
                >
                  {/* Card Header */}
                  <div className="relative bg-gradient-to-br from-orange-400 to-orange-600 p-6 text-white">
                    <div className="absolute top-4 right-4">
                      <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold">#{branch.id}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                        <span className="text-2xl">🥗</span>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold mb-1">{branch.name}</h3>
                        <div className="flex items-center space-x-2">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          <span className="font-medium">{branch.city}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Card Body */}
                  <div className="p-6">
                    {/* Address */}
                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-orange-800 uppercase tracking-wider mb-2">
                        📍 Address
                      </h4>
                      <p className="text-gray-700 leading-relaxed">{branch.address}</p>
                    </div>

                    {/* Location */}
                    <div className="mb-6">
                      <h4 className="text-sm font-semibold text-orange-800 uppercase tracking-wider mb-2">
                        🗺️ Area/Location
                      </h4>
                      <p className="text-orange-700 font-medium">{branch.location}</p>
                    </div>

                    {/* Features */}
                    <div className="mb-6">
                      <div className="grid grid-cols-2 gap-3">
                        <div className="bg-orange-50 rounded-xl p-3 text-center">
                          <span className="text-lg mb-1 block">🍽️</span>
                          <span className="text-xs font-medium text-orange-800">Fresh Meals</span>
                        </div>
                        <div className="bg-orange-50 rounded-xl p-3 text-center">
                          <span className="text-lg mb-1 block">⚡</span>
                          <span className="text-xs font-medium text-orange-800">Quick Service</span>
                        </div>
                        <div className="bg-orange-50 rounded-xl p-3 text-center">
                          <span className="text-lg mb-1 block">🥗</span>
                          <span className="text-xs font-medium text-orange-800">Healthy Options</span>
                        </div>
                        <div className="bg-orange-50 rounded-xl p-3 text-center">
                          <span className="text-lg mb-1 block">🌟</span>
                          <span className="text-xs font-medium text-orange-800">Premium Quality</span>
                        </div>
                      </div>
                    </div>

                    {/* Contact & Actions */}
                    <div className="space-y-3">
                      <a
                        href={`tel:${branch.phone}`}
                        className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold py-3 px-4 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 flex items-center justify-center space-x-2"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        <span>📞 Call {branch.phone}</span>
                      </a>

                      <div className="grid grid-cols-2 gap-3">
                        <button className="bg-orange-100 hover:bg-orange-200 text-orange-800 font-medium py-2 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-1">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3" />
                          </svg>
                          <span className="text-sm">Directions</span>
                        </button>
                        <button className="bg-orange-100 hover:bg-orange-200 text-orange-800 font-medium py-2 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-1">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span className="text-sm">Details</span>
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Card Footer */}
                  <div className="bg-gradient-to-r from-orange-50 to-orange-100 px-6 py-4 border-t border-orange-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium text-orange-800">Open Now</span>
                      </div>
                      <span className="text-xs text-orange-600">LowCalories Branch</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            /* No Results */
            <div className="text-center py-20">
              <div className="max-w-md mx-auto">
                <div className="w-32 h-32 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full mx-auto mb-8 flex items-center justify-center shadow-2xl">
                  <span className="text-5xl">🔍</span>
                </div>
                <h3 className="text-3xl font-bold text-orange-800 mb-4">No Branches Found</h3>
                <p className="text-orange-600 mb-6 text-lg">
                  We couldn't find any LowCalories branches matching your search criteria.
                </p>
                <p className="text-orange-500 mb-8">
                  Try adjusting your search terms or browse all our healthy locations!
                </p>
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setCityFilter('');
                  }}
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 text-lg"
                >
                  🥗 Show All Locations
                </button>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gradient-to-r from-orange-600 to-orange-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Brand */}
            <div className="md:col-span-2">
              <div className="flex items-center mb-6">
                <img
                  src="https://lowcalories.ae/assets/images/logo.png"
                  alt="LowCalories Logo"
                  className="h-12 w-auto mr-4"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.nextElementSibling.style.display = 'flex';
                  }}
                />
                <div className="w-12 h-12 bg-white/20 rounded-xl items-center justify-center mr-4 hidden">
                  <span className="text-2xl">🥗</span>
                </div>
                <h3 className="text-3xl font-bold">LowCalories</h3>
              </div>
              <p className="text-orange-100 text-lg mb-6 leading-relaxed">
                Committed to providing fresh, healthy, and delicious meals that nourish your body and delight your taste buds.
                Visit any of our locations for an exceptional dining experience.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors cursor-pointer">
                  <span>📱</span>
                </div>
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors cursor-pointer">
                  <span>📧</span>
                </div>
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors cursor-pointer">
                  <span>🌐</span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-xl font-bold mb-6">Quick Links</h4>
              <ul className="space-y-3">
                <li><a href="#" className="text-orange-100 hover:text-white transition-colors">Menu</a></li>
                <li><a href="#" className="text-orange-100 hover:text-white transition-colors">Nutrition Info</a></li>
                <li><a href="#" className="text-orange-100 hover:text-white transition-colors">Catering</a></li>
                <li><a href="#" className="text-orange-100 hover:text-white transition-colors">About Us</a></li>
              </ul>
            </div>

            {/* Contact */}
            <div>
              <h4 className="text-xl font-bold mb-6">Contact</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <span>📞</span>
                  <span className="text-orange-100">+971 XXX XXXX</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span>📧</span>
                  <span className="text-orange-100"><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3">
                  <span>🌐</span>
                  <span className="text-orange-100">lowcalories.ae</span>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-orange-500 mt-12 pt-8 text-center">
            <p className="text-orange-200 mb-2">
              &copy; 2025 LowCalories. All rights reserved.
            </p>
            <p className="text-orange-300 text-sm">
              🌱 Committed to your health and wellness • Last updated: {new Date().toLocaleDateString()}
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default LocationsV2;
